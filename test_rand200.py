# -*- coding: utf-8 -*-
import os
import csv
import json
import random
import requests
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm
from PIL import Image
import time

from io_utils import download_image_from_afts, load_image


def load_csv_data(csv_path):
    """加载CSV数据"""
    data = []
    with open(csv_path, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            data.append(row)
    return data


def select_test_data(data, food_count=150, other_count=50):
    """选择测试数据：美食150张，其他类别50张"""
    food_data = [row for row in data if row['cate4_level1_name'] == '美食']
    other_data = [row for row in data if row['cate4_level1_name'] != '美食']
    
    # 随机选择
    selected_food = random.sample(food_data, min(food_count, len(food_data)))
    selected_other = random.sample(other_data, min(other_count, len(other_data)))
    
    return selected_food + selected_other


def call_smart_cover_api(smart_cover_id, title, cat, content_id):
    """调用智能封面增强API"""
    url = "https://paiplusinference.alipay.com/inference/7a2f7401cee56342_smart_cover_enhancement/v1"

    # 构造请求数据
    input_data = {
        "cover_id": smart_cover_id,
        "cover_url": "",
        "title": title,
        "cat": cat,
        "video_name": "",
        "video_id": "",
        "contentId": content_id,
        "logs": {},
        "version": 1
    }

    body = {
        "features": {},
        "tensorFeatures": {
            "data": {
                "shapes": [1],
                "stringValues": [json.dumps(input_data)]
            }
        }
    }

    headers = {
        "Content-Type": "application/json;charset=utf-8",
        "MPS-app-name": "your-app-name",
        "MPS-http-version": "1.0",
        "MPS-trace-id": f"test-{int(time.time())}-{random.randint(1000, 9999)}"
    }

    try:
        response = requests.post(url=url, json=body, headers=headers, timeout=60)
        response.raise_for_status()  # 检查HTTP状态码
        result = response.json()
        return result
    except requests.exceptions.Timeout:
        return {"error": "API调用超时"}
    except requests.exceptions.RequestException as e:
        return {"error": f"网络请求错误: {str(e)}"}
    except json.JSONDecodeError as e:
        return {"error": f"JSON解析错误: {str(e)}"}
    except Exception as e:
        return {"error": f"未知错误: {str(e)}"}


def resize_image_to_match(img1, img2):
    """将img1的尺寸调整为与img2相同"""
    if img1 is None or img2 is None:
        return img1
    
    target_size = img2.size
    return img1.resize(target_size, Image.Resampling.LANCZOS)


def create_combined_image(cover_img, smart_cover_img, new_cover_img, output_path):
    """创建拼接图片：原图 | 智能封面 | 增强后图片"""
    try:
        # 确保所有图片都存在
        if cover_img is None or smart_cover_img is None:
            print(f"缺少必要图片，跳过拼接: {output_path}")
            return False
        
        # 调整原图尺寸与智能封面一致
        cover_img_resized = resize_image_to_match(cover_img, smart_cover_img)
        
        # 如果没有增强图片，只拼接前两张
        if new_cover_img is None:
            images = [cover_img_resized, smart_cover_img]
        else:
            # 调整增强图片尺寸
            new_cover_img_resized = resize_image_to_match(new_cover_img, smart_cover_img)
            images = [cover_img_resized, smart_cover_img, new_cover_img_resized]
        
        # 计算拼接后的尺寸
        width = sum(img.width for img in images)
        height = max(img.height for img in images)
        
        # 创建拼接图片
        combined = Image.new('RGB', (width, height), (255, 255, 255))
        
        x_offset = 0
        for img in images:
            combined.paste(img, (x_offset, 0))
            x_offset += img.width
        
        # 保存图片
        combined.save(output_path)
        return True
        
    except Exception as e:
        print(f"拼接图片失败 {output_path}: {e}")
        return False


def process_single_item(item, output_dir, index):
    """处理单个测试项"""
    try:
        ctt_id = item['ctt_id']
        title = item['ctt_title']
        cat = item['cate4_level1_name']
        cover_id = item['cover_id']
        smart_cover_id = item['smart_cover_id']

        # 调用API
        api_result = call_smart_cover_api(smart_cover_id, title, cat, ctt_id)

        # 下载图片
        cover_img = None
        smart_cover_img = None
        new_cover_img = None

        try:
            if cover_id:
                cover_img = load_image(cover_id)
        except Exception as e:
            print(f"下载原图失败 {cover_id}: {e}")

        try:
            if smart_cover_id:
                smart_cover_img = load_image(smart_cover_id)
        except Exception as e:
            print(f"下载智能封面失败 {smart_cover_id}: {e}")

        # 解析API结果中的新封面ID和增强状态
        new_cover_id = None
        enhanced = False

        if isinstance(api_result, dict):
            # 尝试多种可能的结果格式
            if 'output' in api_result and isinstance(api_result['output'], dict):
                output_data = api_result['output']
                new_cover_id = output_data.get('new_cover_id')
                enhanced = output_data.get('enhanced', False)
            elif 'resultMap' in api_result and 'output' in api_result['resultMap']:
                try:
                    output_data = json.loads(api_result['resultMap']['output'])
                    new_cover_id = output_data.get('new_cover_id')
                    enhanced = output_data.get('enhanced', False)
                except:
                    pass
            elif 'new_cover_id' in api_result:
                new_cover_id = api_result['new_cover_id']
                enhanced = api_result.get('enhanced', False)

        # 如果没有增强或new_cover_id为空，使用smart_cover_id作为最终结果
        final_cover_id = new_cover_id if (enhanced and new_cover_id) else smart_cover_id

        if final_cover_id:
            try:
                if final_cover_id == smart_cover_id:
                    # 如果使用smart_cover，直接复用已下载的图片
                    new_cover_img = smart_cover_img
                else:
                    # 下载增强后的图片
                    new_cover_img = load_image(final_cover_id)
            except Exception as e:
                print(f"下载最终封面图片失败 {final_cover_id}: {e}")
                # 如果下载失败，回退到smart_cover
                new_cover_img = smart_cover_img
                final_cover_id = smart_cover_id

        # 创建拼接图片
        output_filename = f"{index:03d}_{ctt_id}_{cat}.jpg"
        output_path = output_dir / output_filename

        success = create_combined_image(cover_img, smart_cover_img, new_cover_img, output_path)

        # 保存API结果
        result_filename = f"{index:03d}_{ctt_id}_{cat}_result.json"
        result_path = output_dir / result_filename
        with open(result_path, 'w', encoding='utf-8') as f:
            json.dump({
                'input': item,
                'api_result': api_result,
                'image_saved': success,
                'new_cover_id': new_cover_id,
                'final_cover_id': final_cover_id,
                'enhanced': enhanced,
                'used_smart_cover': final_cover_id == smart_cover_id
            }, f, ensure_ascii=False, indent=2)

        return {
            'index': index,
            'ctt_id': ctt_id,
            'cat': cat,
            'success': success,
            'api_result': api_result,
            'new_cover_id': new_cover_id,
            'final_cover_id': final_cover_id,
            'enhanced': enhanced,
            'used_smart_cover': final_cover_id == smart_cover_id
        }

    except Exception as e:
        print(f"处理项目失败 {index}: {e}")
        return {
            'index': index,
            'ctt_id': item.get('ctt_id', 'unknown'),
            'cat': item.get('cate4_level1_name', 'unknown'),
            'success': False,
            'error': str(e)
        }


def main():
    # 设置随机种子确保结果可重现
    random.seed(42)

    # 设置路径
    csv_path = Path("tests/top2000.csv")
    output_dir = Path("tests/rand200")
    output_dir.mkdir(parents=True, exist_ok=True)

    print(f"开始测试智能封面增强服务")
    print(f"输出目录: {output_dir}")
    print(f"时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print("-" * 50)
    
    print("加载CSV数据...")
    data = load_csv_data(csv_path)
    print(f"总共加载 {len(data)} 条数据")
    
    print("选择测试数据...")
    test_data = select_test_data(data, food_count=150, other_count=50)
    print(f"选择了 {len(test_data)} 条测试数据")
    
    # 统计类别分布
    cat_count = {}
    for item in test_data:
        cat = item['cate4_level1_name']
        cat_count[cat] = cat_count.get(cat, 0) + 1
    
    print("测试数据类别分布:")
    for cat, count in sorted(cat_count.items()):
        print(f"  {cat}: {count}")
    
    print(f"\n开始处理 {len(test_data)} 条数据...")
    
    # 使用线程池并发处理
    results = []
    enhanced_count = 0

    with ThreadPoolExecutor(max_workers=3) as executor:  # 降低并发数避免API限流
        # 提交所有任务
        future_to_index = {
            executor.submit(process_single_item, item, output_dir, i): i
            for i, item in enumerate(test_data, 1)
        }

        # 使用tqdm显示进度
        with tqdm(total=len(test_data), desc="处理进度",
                 bar_format='{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}, {rate_fmt}]') as pbar:
            for future in as_completed(future_to_index):
                result = future.result()
                results.append(result)

                if result.get('enhanced'):
                    enhanced_count += 1

                pbar.update(1)

                # 显示当前处理结果
                status = "✓" if result['success'] else "✗"
                enhanced_status = "🔥" if result.get('enhanced') else ("📋" if result.get('used_smart_cover') else "")
                pbar.set_postfix({
                    "当前": f"{result['index']:03d}_{result['cat'][:4]}{status}{enhanced_status}",
                    "增强": f"{enhanced_count}/{len(results)}"
                })

                # 添加小延迟避免API限流
                time.sleep(0.1)

    # 统计结果
    success_count = sum(1 for r in results if r['success'])
    fail_count = len(results) - success_count
    smart_cover_count = sum(1 for r in results if r.get('used_smart_cover'))

    print(f"\n处理完成!")
    print(f"总数: {len(results)}")
    print(f"成功: {success_count}")
    print(f"失败: {fail_count}")
    print(f"增强: {enhanced_count}")
    print(f"使用智能封面: {smart_cover_count}")
    print(f"增强率: {enhanced_count/len(results)*100:.1f}%")
    print(f"智能封面使用率: {smart_cover_count/len(results)*100:.1f}%")
    print(f"结果保存在: {output_dir}")

    # 保存汇总结果
    summary_path = output_dir / "summary.json"
    with open(summary_path, 'w', encoding='utf-8') as f:
        json.dump({
            'total': len(results),
            'success': success_count,
            'failed': fail_count,
            'enhanced': enhanced_count,
            'used_smart_cover': smart_cover_count,
            'enhancement_rate': enhanced_count/len(results)*100,
            'smart_cover_usage_rate': smart_cover_count/len(results)*100,
            'category_distribution': cat_count,
            'results': sorted(results, key=lambda x: x['index'])
        }, f, ensure_ascii=False, indent=2)

    print(f"汇总结果保存在: {summary_path}")

    # 显示失败的项目
    failed_items = [r for r in results if not r['success']]
    if failed_items:
        print(f"\n失败的项目 ({len(failed_items)}):")
        for item in failed_items[:10]:  # 只显示前10个
            print(f"  {item['index']:03d}: {item['ctt_id']} - {item.get('error', 'Unknown error')}")
        if len(failed_items) > 10:
            print(f"  ... 还有 {len(failed_items) - 10} 个失败项目")


if __name__ == "__main__":
    main()
