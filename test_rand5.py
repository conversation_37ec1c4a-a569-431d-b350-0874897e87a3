# -*- coding: utf-8 -*-
"""
简化版测试脚本，用于测试5条数据
"""
import os
import csv
import json
import random
import requests
from pathlib import Path
from tqdm import tqdm
from PIL import Image
import time

from io_utils import download_image_from_afts, load_image


def load_csv_data(csv_path):
    """加载CSV数据"""
    data = []
    with open(csv_path, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            data.append(row)
    return data


def call_smart_cover_api(smart_cover_id, title, cat, content_id):
    """调用智能封面增强API"""
    url = "https://paiplusinference.alipay.com/inference/7a2f7401cee56342_smart_cover_enhancement/v1"
    
    # 构造请求数据
    input_data = {
        "cover_id": smart_cover_id,
        "cover_url": "",
        "title": title,
        "cat": cat,
        "video_name": "",
        "video_id": "",
        "contentId": content_id,
        "logs": {},
        "version": 1
    }
    
    body = {
        "features": {},
        "tensorFeatures": {
            "data": {
                "shapes": [1],
                "stringValues": [json.dumps(input_data)]
            }
        }
    }
    
    headers = {
        "Content-Type": "application/json;charset=utf-8",
        "MPS-app-name": "your-app-name",
        "MPS-http-version": "1.0",
        "MPS-trace-id": f"test-{int(time.time())}-{random.randint(1000, 9999)}"
    }
    
    try:
        print(f"调用API: {smart_cover_id}")
        response = requests.post(url=url, json=body, headers=headers, timeout=60)
        response.raise_for_status()
        result = response.json()
        print(f"API响应: {result}")
        return result
    except Exception as e:
        print(f"API调用失败: {e}")
        return {"error": str(e)}


def resize_image_to_match(img1, img2):
    """将img1的尺寸调整为与img2相同"""
    if img1 is None or img2 is None:
        return img1
    
    target_size = img2.size
    return img1.resize(target_size, Image.Resampling.LANCZOS)


def create_combined_image(cover_img, smart_cover_img, new_cover_img, output_path):
    """创建拼接图片：原图 | 智能封面 | 增强后图片"""
    try:
        # 确保所有图片都存在
        if cover_img is None or smart_cover_img is None:
            print(f"缺少必要图片，跳过拼接: {output_path}")
            return False
        
        # 调整原图尺寸与智能封面一致
        cover_img_resized = resize_image_to_match(cover_img, smart_cover_img)
        
        # 如果没有增强图片，只拼接前两张
        if new_cover_img is None:
            images = [cover_img_resized, smart_cover_img]
        else:
            # 调整增强图片尺寸
            new_cover_img_resized = resize_image_to_match(new_cover_img, smart_cover_img)
            images = [cover_img_resized, smart_cover_img, new_cover_img_resized]
        
        # 计算拼接后的尺寸
        width = sum(img.width for img in images)
        height = max(img.height for img in images)
        
        # 创建拼接图片
        combined = Image.new('RGB', (width, height), (255, 255, 255))
        
        x_offset = 0
        for img in images:
            combined.paste(img, (x_offset, 0))
            x_offset += img.width
        
        # 保存图片
        combined.save(output_path)
        print(f"拼接图片保存成功: {output_path}")
        return True
        
    except Exception as e:
        print(f"拼接图片失败 {output_path}: {e}")
        return False


def main():
    # 设置随机种子
    random.seed(42)
    
    # 设置路径
    csv_path = Path("tests/top2000.csv")
    output_dir = Path("tests/rand5")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"开始测试智能封面增强服务 (5条数据)")
    print(f"输出目录: {output_dir}")
    print("-" * 50)
    
    # 加载数据
    data = load_csv_data(csv_path)
    print(f"总共加载 {len(data)} 条数据")
    
    # 选择测试数据：美食3条，其他2条
    food_data = [row for row in data if row['cate4_level1_name'] == '美食']
    other_data = [row for row in data if row['cate4_level1_name'] != '美食']
    
    test_data = random.sample(food_data, 3) + random.sample(other_data, 2)
    print(f"选择了 {len(test_data)} 条测试数据")
    
    # 处理每条数据
    results = []
    for i, item in enumerate(test_data, 1):
        print(f"\n处理第 {i}/{len(test_data)} 条数据:")
        print(f"  ID: {item['ctt_id']}")
        print(f"  标题: {item['ctt_title']}")
        print(f"  类别: {item['cate4_level1_name']}")
        
        try:
            # 调用API
            api_result = call_smart_cover_api(
                item['smart_cover_id'], 
                item['ctt_title'], 
                item['cate4_level1_name'], 
                item['ctt_id']
            )
            
            # 下载图片
            print("  下载图片...")
            cover_img = load_image(item['cover_id']) if item['cover_id'] else None
            smart_cover_img = load_image(item['smart_cover_id']) if item['smart_cover_id'] else None
            
            # 解析新封面ID和增强状态
            new_cover_id = None
            enhanced = False
            new_cover_img = None

            if isinstance(api_result, dict):
                # 尝试多种可能的结果格式
                if 'output' in api_result:
                    output_data = api_result['output']
                    if isinstance(output_data, dict):
                        new_cover_id = output_data.get('new_cover_id')
                        enhanced = output_data.get('enhanced', False)
                    elif isinstance(output_data, str):
                        try:
                            parsed_output = json.loads(output_data)
                            new_cover_id = parsed_output.get('new_cover_id')
                            enhanced = parsed_output.get('enhanced', False)
                        except:
                            pass
                elif 'new_cover_id' in api_result:
                    new_cover_id = api_result['new_cover_id']
                    enhanced = api_result.get('enhanced', False)

            # 如果没有增强或new_cover_id为空，使用smart_cover_id作为最终结果
            final_cover_id = new_cover_id if (enhanced and new_cover_id) else item['smart_cover_id']

            if final_cover_id:
                if final_cover_id == item['smart_cover_id']:
                    print(f"  使用智能封面: {final_cover_id}")
                    new_cover_img = smart_cover_img
                else:
                    print(f"  下载增强图片: {final_cover_id}")
                    new_cover_img = load_image(final_cover_id)
            else:
                print("  未获得最终图片")
            
            # 创建拼接图片
            output_filename = f"{i:03d}_{item['ctt_id']}_{item['cate4_level1_name']}.jpg"
            output_path = output_dir / output_filename
            
            success = create_combined_image(cover_img, smart_cover_img, new_cover_img, output_path)
            
            # 保存结果
            result_filename = f"{i:03d}_{item['ctt_id']}_result.json"
            result_path = output_dir / result_filename
            with open(result_path, 'w', encoding='utf-8') as f:
                json.dump({
                    'input': item,
                    'api_result': api_result,
                    'new_cover_id': new_cover_id,
                    'final_cover_id': final_cover_id,
                    'enhanced': enhanced,
                    'used_smart_cover': final_cover_id == item['smart_cover_id'],
                    'image_saved': success
                }, f, ensure_ascii=False, indent=2)

            results.append({
                'index': i,
                'ctt_id': item['ctt_id'],
                'cat': item['cate4_level1_name'],
                'success': success,
                'enhanced': enhanced,
                'used_smart_cover': final_cover_id == item['smart_cover_id']
            })

            status_msg = "成功" if success else "失败"
            enhance_msg = "已增强" if enhanced else ("使用智能封面" if final_cover_id == item['smart_cover_id'] else "未处理")
            print(f"  结果: {status_msg}, {enhance_msg}")
            
        except Exception as e:
            print(f"  处理失败: {e}")
            results.append({
                'index': i,
                'ctt_id': item['ctt_id'],
                'cat': item['cate4_level1_name'],
                'success': False,
                'error': str(e)
            })
        
        # 添加延迟
        time.sleep(1)
    
    # 统计结果
    success_count = sum(1 for r in results if r['success'])
    enhanced_count = sum(1 for r in results if r.get('enhanced'))
    smart_cover_count = sum(1 for r in results if r.get('used_smart_cover'))

    print(f"\n" + "="*50)
    print(f"测试完成!")
    print(f"总数: {len(results)}")
    print(f"成功: {success_count}")
    print(f"失败: {len(results) - success_count}")
    print(f"增强: {enhanced_count}")
    print(f"使用智能封面: {smart_cover_count}")
    print(f"结果保存在: {output_dir}")


if __name__ == "__main__":
    main()
