# 智能封面增强测试脚本

## 概述

本项目包含两个测试脚本，用于测试智能封面增强服务的API调用和图片处理功能。

## 脚本说明

### 1. test_rand5.py - 简化版测试脚本
- **用途**: 测试5条数据（美食3条，其他类别2条）
- **推荐**: 首次运行时使用，用于验证API和图片处理功能
- **输出目录**: `tests/rand5/`

### 2. test_rand200.py - 完整版测试脚本  
- **用途**: 测试200条数据（美食150条，其他类别50条）
- **特点**: 使用并发处理，包含完整的统计分析
- **输出目录**: `tests/rand200/`

## 核心功能

### API调用逻辑
1. 使用POST方式调用智能封面增强服务
2. 传入参数：`smart_cover_id`, `title`, `cat`, `contentId`
3. 解析返回结果中的`enhanced`状态和`new_cover_id`

### 图片处理逻辑
根据API返回结果决定最终使用的封面：

```python
# 如果API返回enhanced=True且new_cover_id不为空
if enhanced and new_cover_id:
    final_cover_id = new_cover_id  # 使用增强后的图片
else:
    final_cover_id = smart_cover_id  # 使用智能封面
```

### 图片拼接
生成的拼接图片包含三部分：
- **左侧**: 原图（cover_id，调整尺寸与智能封面一致）
- **中间**: 智能封面（smart_cover_id）
- **右侧**: 最终结果（增强图片或智能封面）

## 使用方法

### 1. 首次测试（推荐）
```bash
python test_rand5.py
```

### 2. 完整测试
```bash
python test_rand200.py
```

## 输出结果

### 文件结构
```
tests/rand5/ (或 tests/rand200/)
├── 001_contentId_美食.jpg          # 拼接后的图片
├── 001_contentId_美食_result.json  # 详细结果
├── 002_contentId_政务.jpg
├── 002_contentId_政务_result.json
├── ...
└── summary.json                    # 汇总统计
```

### 结果JSON格式
```json
{
  "input": {
    "ctt_id": "...",
    "ctt_title": "...",
    "cate4_level1_name": "美食",
    "cover_id": "...",
    "smart_cover_id": "..."
  },
  "api_result": {
    "output": {
      "new_cover_id": "...",
      "enhanced": true,
      "enhance_list": ["food"]
    }
  },
  "new_cover_id": "...",
  "final_cover_id": "...",
  "enhanced": true,
  "used_smart_cover": false,
  "image_saved": true
}
```

### 统计信息
- **总数**: 处理的数据条数
- **成功**: 成功处理的数量
- **失败**: 处理失败的数量  
- **增强**: API返回enhanced=true的数量
- **使用智能封面**: 最终使用smart_cover_id的数量
- **增强率**: 增强数量/总数 * 100%
- **智能封面使用率**: 使用智能封面数量/总数 * 100%

## 进度显示

### 完整版脚本进度条说明
```
处理进度: 45%|████▌     | 90/200 [02:15<02:45, 0.67it/s] 当前: 090_美食✓🔥, 增强: 23/90
```

- **✓**: 处理成功
- **✗**: 处理失败  
- **🔥**: 图片已增强
- **📋**: 使用智能封面

## 配置说明

### 并发设置
- **完整版**: 3个并发线程（避免API限流）
- **简化版**: 单线程顺序处理

### 延迟设置
- 每个请求间隔0.1秒（完整版）或1秒（简化版）

### 随机种子
- 固定种子值42，确保结果可重现

## 错误处理

1. **API调用错误**: 超时、网络错误、JSON解析错误
2. **图片下载错误**: 自动回退到智能封面
3. **图片拼接错误**: 记录错误信息，继续处理下一项

## 依赖要求

```python
requests
tqdm
Pillow
concurrent.futures (内置)
```

## 注意事项

1. 确保`io_utils.py`中的AFTS配置正确
2. API服务地址和认证信息需要有效
3. 网络连接稳定，避免下载图片失败
4. 磁盘空间充足，用于保存拼接图片和结果文件

## 故障排除

### 常见问题
1. **API调用失败**: 检查网络连接和服务地址
2. **图片下载失败**: 检查AFTS配置和图片ID有效性
3. **内存不足**: 降低并发数或分批处理
4. **磁盘空间不足**: 清理输出目录或增加存储空间
